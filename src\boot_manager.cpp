#include "boot_manager.h"

BootManager::BootManager() {
    mSystemTable = NULL;
    mBootServices = NULL;
    mRuntimeServices = NULL;
    mBootOptions = NULL;
    mBootOptionCount = 0;
    mSelectedOption = 0;
    mDefaultTimeout = DEFAULT_TIMEOUT_SECONDS;

    // 新增：初始化主题和语言设置
    mCurrentTheme = THEME_DARK;  // 默认深色主题
    mCurrentLanguage = LANG_CHINESE;  // 默认中文
    mShowSettings = FALSE;
}

BootManager::~BootManager() {
    if (mBootOptions) {
        for (UINTN i = 0; i < mBootOptionCount; i++) {
            if (mBootOptions[i].Name) {
                FreePool(mBootOptions[i].Name);
            }
            if (mBootOptions[i].Description) {
                FreePool(mBootOptions[i].Description);
            }
        }
        FreePool(mBootOptions);
    }
}

EFI_STATUS BootManager::Initialize(EFI_SYSTEM_TABLE *SystemTable) {
    if (!SystemTable) {
        return EFI_INVALID_PARAMETER;
    }
    
    mSystemTable = SystemTable;
    mBootServices = SystemTable->BootServices;
    mRuntimeServices = SystemTable->RuntimeServices;
    
    // 加载配置
    LoadConfiguration();
    
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::ScanBootOptions() {
    EFI_STATUS Status;
    
    Print(L"Scanning for boot options...\n");
    
    // 扫描文件系统
    Status = ScanFileSystem();
    if (EFI_ERROR(Status)) {
        Print(L"Failed to scan file systems: %r\n", Status);
        return Status;
    }
    
    // 添加现代化的引导选项
    AddBootOption(L"空旷系统 20.0", L"Spacious System 20.0", NULL);
    AddBootOption(L"Manjaro Linux 18.0", L"Manjaro Linux Distribution", NULL);
    AddBootOption(L"Ubuntu 24.01", L"Ubuntu Linux Distribution", NULL);
    AddBootOption(L"Windows 11", L"Microsoft Windows 11", NULL);
    AddBootOption(L"Fedora 42.0", L"Fedora Linux Distribution", NULL);
    AddBootOption(L"UEFI Shell", L"EFI Shell Environment", NULL);
    
    Print(L"Found %d boot options\n", mBootOptionCount);
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::ScanFileSystem() {
    EFI_STATUS Status;
    UINTN HandleCount;
    EFI_HANDLE *HandleBuffer;
    
    // 获取所有简单文件系统协议句柄
    Status = mBootServices->LocateHandleBuffer(
        ByProtocol,
        &gEfiSimpleFileSystemProtocolGuid,
        NULL,
        &HandleCount,
        &HandleBuffer
    );
    
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    // 遍历每个文件系统
    for (UINTN i = 0; i < HandleCount; i++) {
        // 检查是否有bootmgfw.efi或其他引导文件
        LoadBootOption(HandleBuffer[i], L"\\EFI\\Microsoft\\Boot\\bootmgfw.efi");
        LoadBootOption(HandleBuffer[i], L"\\EFI\\Boot\\bootx64.efi");
    }
    
    if (HandleBuffer) {
        FreePool(HandleBuffer);
    }
    
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::LoadBootOption(EFI_HANDLE Handle, CHAR16 *Path) {
    // 这里应该检查文件是否存在，并创建相应的引导选项
    // 简化实现，实际应该打开文件系统并检查文件
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::AddBootOption(CHAR16 *Name, CHAR16 *Description, EFI_DEVICE_PATH *DevicePath) {
    EFI_STATUS Status;
    BOOT_OPTION *NewOptions;
    
    // 重新分配内存
    Status = mBootServices->AllocatePool(
        EfiBootServicesData,
        (mBootOptionCount + 1) * sizeof(BOOT_OPTION),
        (VOID**)&NewOptions
    );
    
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    // 复制现有选项
    if (mBootOptions) {
        CopyMem(NewOptions, mBootOptions, mBootOptionCount * sizeof(BOOT_OPTION));
        FreePool(mBootOptions);
    }
    
    mBootOptions = NewOptions;
    
    // 添加新选项
    BOOT_OPTION *NewOption = &mBootOptions[mBootOptionCount];
    
    // 分配并复制名称
    Status = mBootServices->AllocatePool(
        EfiBootServicesData,
        (StrLen(Name) + 1) * sizeof(CHAR16),
        (VOID**)&NewOption->Name
    );
    if (!EFI_ERROR(Status)) {
        StrCpy(NewOption->Name, Name);
    }
    
    // 分配并复制描述
    Status = mBootServices->AllocatePool(
        EfiBootServicesData,
        (StrLen(Description) + 1) * sizeof(CHAR16),
        (VOID**)&NewOption->Description
    );
    if (!EFI_ERROR(Status)) {
        StrCpy(NewOption->Description, Description);
    }
    
    NewOption->DevicePath = DevicePath;
    NewOption->ImageHandle = NULL;
    NewOption->IsDefault = (mBootOptionCount == 0); // 第一个选项为默认
    NewOption->IsActive = TRUE;
    
    mBootOptionCount++;
    
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::ShowBootMenu() {
    if (mBootOptionCount == 0) {
        Print(L"No boot options found!\n");
        return EFI_NOT_FOUND;
    }

    RefreshScreen();
    return EFI_SUCCESS;
}

VOID BootManager::RefreshScreen() {
    if (mShowSettings) {
        DrawSettingsPanel();
    } else {
        DrawModernInterface();
    }
}

VOID BootManager::DrawModernInterface() {
    ClearScreen();
    SetConsoleColors();

    // 设置光标位置到顶部中央
    mSystemTable->ConOut->SetCursorPosition(mSystemTable->ConOut, 0, 2);

    // 显示系统标题
    CHAR16* title = GetLocalizedText(TITLE_TEXT_CN, TITLE_TEXT_EN);
    CHAR16* subtitle = GetLocalizedText(SUBTITLE_TEXT_CN, SUBTITLE_TEXT_EN);

    Print(L"                    %s\n", title);
    Print(L"                 %s\n\n", subtitle);

    // 显示引导选项卡片
    Print(L"  ┌─────────────────────────────────────────────────────────────┐\n");
    Print(L"  │                                                             │\n");

    for (UINTN i = 0; i < mBootOptionCount; i++) {
        DisplayBootOption(i, (i == mSelectedOption));
    }

    Print(L"  │                                                             │\n");
    Print(L"  └─────────────────────────────────────────────────────────────┘\n\n");

    // 显示倒计时和帮助信息
    CHAR16* timeoutText = GetLocalizedText(TIMEOUT_TEXT_CN, TIMEOUT_TEXT_EN);
    CHAR16* settingsText = GetLocalizedText(SETTINGS_TEXT_CN, SETTINGS_TEXT_EN);

    Print(L"                 %s\n\n", timeoutText, mDefaultTimeout);
    Print(L"              %s\n", settingsText);
    Print(L"         ENTER=启动  ESC=退出  ↑↓=选择\n");
}

VOID BootManager::DisplayBootOption(UINTN Index, BOOLEAN Selected) {
    if (Index >= mBootOptionCount) {
        return;
    }

    BOOT_OPTION *Option = &mBootOptions[Index];

    // 根据操作系统类型选择图标
    CHAR16* icon = L"▲";  // 默认图标
    if (StrStr(Option->Name, L"Windows")) {
        icon = L"⊞";  // Windows图标
    } else if (StrStr(Option->Name, L"Linux") || StrStr(Option->Name, L"Ubuntu") ||
               StrStr(Option->Name, L"Manjaro") || StrStr(Option->Name, L"Fedora")) {
        icon = L"🐧";  // Linux图标
    } else if (StrStr(Option->Name, L"空旷") || StrStr(Option->Name, L"Spacious")) {
        icon = L"◆";  // 空旷系统图标
    } else if (StrStr(Option->Name, L"Shell")) {
        icon = L"⚙";  // Shell图标
    }

    if (Selected) {
        // 选中状态 - 蓝色高亮
        Print(L"  │  ▶ %s  %s                                    │\n", icon, Option->Name);
        Print(L"  │      %s                                      │\n", Option->Description);
    } else {
        // 未选中状态
        Print(L"  │    %s  %s                                      │\n", icon, Option->Name);
        Print(L"  │      %s                                      │\n", Option->Description);
    }
}

EFI_STATUS BootManager::ProcessUserSelection() {
    EFI_STATUS Status;
    EFI_INPUT_KEY Key;
    UINTN TimeoutCounter = mDefaultTimeout;

    while (TRUE) {
        // 检查是否有按键输入
        Status = mSystemTable->ConIn->ReadKeyStroke(mSystemTable->ConIn, &Key);

        if (Status == EFI_SUCCESS) {
            // 处理按键
            switch (Key.ScanCode) {
                case SCAN_UP:
                    if (mSelectedOption > 0) {
                        mSelectedOption--;
                        RefreshScreen();
                    }
                    break;

                case SCAN_DOWN:
                    if (mSelectedOption < mBootOptionCount - 1) {
                        mSelectedOption++;
                        RefreshScreen();
                    }
                    break;

                case SCAN_ESC:
                    Print(L"Boot cancelled by user.\n");
                    return EFI_ABORTED;

                default:
                    break;
            }

            switch (Key.UnicodeChar) {
                case CHAR_CARRIAGE_RETURN:
                    // 启动选中的引导选项
                    return LaunchBootOption(mSelectedOption);

                case L'e':
                case L'E':
                    // 切换主题
                    ToggleTheme();
                    RefreshScreen();
                    break;

                case L'l':
                case L'L':
                    // 切换语言
                    ToggleLanguage();
                    RefreshScreen();
                    break;

                case CHAR_TAB:
                    // 显示/隐藏设置面板
                    mShowSettings = !mShowSettings;
                    RefreshScreen();
                    break;

                default:
                    break;
            }

            // 重置超时计数器
            TimeoutCounter = mDefaultTimeout;
        } else if (Status == EFI_NOT_READY) {
            // 没有按键，等待1秒
            mBootServices->Stall(1000000); // 1秒 = 1,000,000微秒
            TimeoutCounter--;

            if (TimeoutCounter == 0) {
                // 超时，启动默认选项
                Print(L"Timeout reached, booting default option...\n");
                return LaunchBootOption(mSelectedOption);
            }

            // 更新超时显示
            RefreshScreen();
        } else {
            // 其他错误
            Print(L"Error reading input: %r\n", Status);
            return Status;
        }
    }

    return EFI_SUCCESS;
}

EFI_STATUS BootManager::LaunchBootOption(UINTN Index) {
    if (Index >= mBootOptionCount) {
        return EFI_INVALID_PARAMETER;
    }

    BOOT_OPTION *Option = &mBootOptions[Index];

    Print(L"Launching: %s\n", Option->Name);
    Print(L"Description: %s\n", Option->Description);

    // 这里应该实际启动引导选项
    // 简化实现，实际应该加载并执行EFI应用程序
    Print(L"Boot option launch not fully implemented yet.\n");
    Print(L"This would normally chain-load the selected bootloader.\n");

    // 等待用户按键
    Print(L"Press any key to continue...\n");
    EFI_INPUT_KEY Key;
    while (mSystemTable->ConIn->ReadKeyStroke(mSystemTable->ConIn, &Key) != EFI_SUCCESS) {
        mBootServices->Stall(100000); // 100ms
    }

    return EFI_SUCCESS;
}

EFI_STATUS BootManager::SetDefaultOption(UINTN Index) {
    if (Index >= mBootOptionCount) {
        return EFI_INVALID_PARAMETER;
    }

    // 清除所有默认标记
    for (UINTN i = 0; i < mBootOptionCount; i++) {
        mBootOptions[i].IsDefault = FALSE;
    }

    // 设置新的默认选项
    mBootOptions[Index].IsDefault = TRUE;
    mSelectedOption = Index;

    return EFI_SUCCESS;
}

EFI_STATUS BootManager::SetTimeout(UINTN Seconds) {
    mDefaultTimeout = Seconds;
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::LoadConfiguration() {
    // 简化实现，使用默认配置
    // 实际应该从NVRAM或配置文件加载
    mDefaultTimeout = 30;
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::SaveConfiguration() {
    // 简化实现
    // 实际应该保存到NVRAM或配置文件
    return EFI_SUCCESS;
}

// 新增方法实现

EFI_STATUS BootManager::ToggleTheme() {
    mCurrentTheme = (mCurrentTheme == THEME_DARK) ? THEME_LIGHT : THEME_DARK;
    SetTheme(mCurrentTheme);
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::ToggleLanguage() {
    mCurrentLanguage = (mCurrentLanguage == LANG_CHINESE) ? LANG_ENGLISH : LANG_CHINESE;
    SetLanguage(mCurrentLanguage);
    return EFI_SUCCESS;
}

VOID BootManager::SetTheme(THEME_MODE Theme) {
    mCurrentTheme = Theme;
    SetConsoleColors();
}

VOID BootManager::SetLanguage(LANGUAGE_MODE Language) {
    mCurrentLanguage = Language;
}

VOID BootManager::SetConsoleColors() {
    // 根据主题设置控制台颜色
    if (mCurrentTheme == THEME_DARK) {
        mSystemTable->ConOut->SetAttribute(mSystemTable->ConOut,
            EFI_TEXT_ATTR(EFI_LIGHTGRAY, EFI_BLACK));
    } else {
        mSystemTable->ConOut->SetAttribute(mSystemTable->ConOut,
            EFI_TEXT_ATTR(EFI_BLACK, EFI_LIGHTGRAY));
    }
}

CHAR16* BootManager::GetLocalizedText(CHAR16* ChineseText, CHAR16* EnglishText) {
    return (mCurrentLanguage == LANG_CHINESE) ? ChineseText : EnglishText;
}

VOID BootManager::ClearScreen() {
    mSystemTable->ConOut->ClearScreen(mSystemTable->ConOut);
    SetConsoleColors();
}

VOID BootManager::DrawSettingsPanel() {
    ClearScreen();

    // 设置面板标题
    CHAR16* title = GetLocalizedText(L"设置", L"Settings");
    Print(L"\n                    %s\n", title);
    Print(L"  ═══════════════════════════════════════════════════════════\n\n");

    // 主题设置
    CHAR16* themeLabel = GetLocalizedText(L"主题模式", L"Theme Mode");
    CHAR16* currentThemeText = (mCurrentTheme == THEME_DARK) ?
        GetLocalizedText(THEME_DARK_CN, THEME_DARK_EN) :
        GetLocalizedText(THEME_LIGHT_CN, THEME_LIGHT_EN);

    Print(L"  %s: %s\n", themeLabel, currentThemeText);
    Print(L"  按 E 键切换主题 / Press E to toggle theme\n\n");

    // 语言设置
    CHAR16* langLabel = GetLocalizedText(L"语言", L"Language");
    CHAR16* currentLangText = (mCurrentLanguage == LANG_CHINESE) ? L"中文" : L"English";

    Print(L"  %s: %s\n", langLabel, currentLangText);
    Print(L"  按 L 键切换语言 / Press L to toggle language\n\n");

    // 系统信息
    CHAR16* sysInfoLabel = GetLocalizedText(L"系统信息", L"System Information");
    Print(L"  %s:\n", sysInfoLabel);
    Print(L"  ─────────────────────────────────────────────────────────\n");
    Print(L"  系统名称 / System Name: %s\n", SYSTEM_NAME);
    Print(L"  版本 / Version: %s\n", BOOTMGR_VERSION_STRING);
    Print(L"  引导选项数量 / Boot Options: %d\n", mBootOptionCount);
    Print(L"  当前主题 / Current Theme: %s\n", currentThemeText);
    Print(L"  当前语言 / Current Language: %s\n", currentLangText);

    Print(L"\n  按 TAB 返回主界面 / Press TAB to return to main menu\n");
    Print(L"  按 ESC 退出 / Press ESC to exit\n");
}

EFI_STATUS BootManager::ShowSettingsMenu() {
    mShowSettings = TRUE;
    RefreshScreen();
    return EFI_SUCCESS;
}

THEME_MODE BootManager::GetCurrentTheme() {
    return mCurrentTheme;
}

LANGUAGE_MODE BootManager::GetCurrentLanguage() {
    return mCurrentLanguage;
}
