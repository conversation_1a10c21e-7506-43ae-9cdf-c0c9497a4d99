# Windows 环境下的空旷系统 EFI 开发环境设置

## 🚨 当前问题
你的系统缺少 GCC 编译器，这是编译 EFI 应用程序必需的工具。

## 🛠️ 解决方案

### 方案1：安装 MSYS2 (推荐)

MSYS2 是 Windows 下最好的 Unix 环境，包含完整的 GCC 工具链。

#### 步骤1：下载并安装 MSYS2
1. 访问：https://www.msys2.org/
2. 下载 `msys2-x86_64-*.exe`
3. 运行安装程序，使用默认设置

#### 步骤2：安装开发工具
打开 MSYS2 终端，运行以下命令：

```bash
# 更新包管理器
pacman -Syu

# 安装基础开发工具
pacman -S base-devel

# 安装 MinGW-w64 GCC 工具链
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-make

# 安装 GNU-EFI 开发库
pacman -S mingw-w64-x86_64-gnu-efi
```

#### 步骤3：设置环境变量
将以下路径添加到系统 PATH：
```
C:\msys64\mingw64\bin
C:\msys64\usr\bin
```

### 方案2：安装 MinGW-w64 独立版

#### 步骤1：下载 MinGW-w64
1. 访问：https://www.mingw-w64.org/downloads/
2. 选择 "MingW-W64-builds"
3. 下载并安装

#### 步骤2：手动安装 GNU-EFI
由于 GNU-EFI 在 Windows 下比较复杂，建议使用 MSYS2。

### 方案3：使用 Visual Studio + Clang (高级)

如果你有 Visual Studio，可以安装 Clang 工具链：
1. 安装 Visual Studio 2022
2. 安装 "C++ Clang tools for Windows"
3. 手动配置 GNU-EFI 库

## 🚀 验证安装

安装完成后，在命令提示符中运行：

```cmd
gcc --version
make --version
```

应该看到版本信息输出。

## 📋 快速测试

安装完成后，运行：

```cmd
# 使用简化构建脚本
.\build_simple.bat

# 或者使用原始构建脚本
.\build.bat
```

## 🐳 替代方案：使用 Docker

如果安装本地工具链有困难，可以使用 Docker：

### 创建 Dockerfile
```dockerfile
FROM ubuntu:22.04

RUN apt-get update && apt-get install -y \
    build-essential \
    gnu-efi \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /workspace
```

### 构建和运行
```cmd
# 构建 Docker 镜像
docker build -t efi-builder .

# 运行构建
docker run -v %cd%:/workspace efi-builder make all
```

## 🌐 在线编译服务

如果本地环境设置困难，可以考虑使用在线编译服务：

1. **GitHub Codespaces**
   - 在 GitHub 上 fork 项目
   - 使用 Codespaces 在线开发

2. **Gitpod**
   - 访问：https://gitpod.io
   - 导入项目进行在线编译

3. **Replit**
   - 访问：https://replit.com
   - 创建 C++ 项目并导入代码

## 📝 预编译版本

如果你只想测试功能，我可以为你提供预编译的 EFI 文件：

### 下载预编译版本
```
bin/bootmgr.efi - 空旷系统 EFI 引导管理器
```

### 直接测试
1. 将 `bootmgr.efi` 复制到 USB 的 `\EFI\Boot\bootx64.efi`
2. 在支持 UEFI 的电脑上从 USB 启动测试

## 🔧 故障排除

### 问题1：找不到 efi.h
```
解决：确保安装了 gnu-efi 开发包
MSYS2: pacman -S mingw-w64-x86_64-gnu-efi
```

### 问题2：链接错误
```
解决：检查 Makefile 中的库路径设置
可能需要调整 GNUEFI_LIB_DIR 路径
```

### 问题3：字符编码问题
```
解决：使用 build_simple.bat 而不是 build.bat
或者将文件保存为 UTF-8 编码
```

## 📞 获取帮助

如果遇到问题：

1. **检查依赖**：运行 `build_simple.bat check`
2. **查看日志**：构建失败时会显示详细错误信息
3. **在线求助**：在项目 Issues 中提问

## 🎯 推荐流程

对于 Windows 用户，我推荐以下流程：

1. **安装 MSYS2**（最简单）
2. **验证工具链**
3. **运行构建脚本**
4. **测试 EFI 文件**

这样你就可以在 Windows 下成功编译和测试"空旷系统"EFI 引导管理器了！
