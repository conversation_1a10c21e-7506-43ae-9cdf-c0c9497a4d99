# 空旷系统 EFI 引导管理器 - 快速开始

## 🚀 5分钟快速体验

### 第一步：构建项目
```bash
# Windows 用户
build.bat

# Linux 用户  
./build.sh
```

### 第二步：测试运行
```bash
# Windows 用户
test.bat

# 选择选项 1 进行 QEMU 测试
```

### 第三步：体验功能

#### 🎨 主题切换演示
1. 启动后看到深色主题界面
2. 按 `E` 键切换到浅色主题
3. 再按 `E` 键切换回深色主题

#### 🌍 语言切换演示
1. 默认显示中文界面
2. 按 `L` 键切换到英文界面
3. 再按 `L` 键切换回中文界面

#### ⚙️ 设置面板演示
1. 按 `TAB` 键打开设置面板
2. 查看系统信息和当前配置
3. 再按 `TAB` 键返回主界面

#### 🖱️ 引导选择演示
1. 使用 `↑↓` 方向键选择不同系统
2. 观察选中项的高亮效果
3. 按 `ENTER` 模拟启动（演示模式）

## 📋 界面预览

### 主界面（深色主题 - 中文）
```
                    空旷系统
                 选择您的系统

  ┌─────────────────────────────────────────────────────────────┐
  │  ▶ ◆  空旷系统 20.0                                         │
  │      Spacious System 20.0                                   │
  │    🐧  Manjaro Linux 18.0                                   │
  │      Manjaro Linux Distribution                             │
  │    🐧  Ubuntu 24.01                                         │
  │      Ubuntu Linux Distribution                              │
  │    ⊞  Windows 11                                            │
  │      Microsoft Windows 11                                   │
  └─────────────────────────────────────────────────────────────┘

                 自动启动倒计时: 30 秒
              E - 设置  L - 语言
```

## 🎯 核心特性体验

### 1. 现代化界面
- ✅ 卡片式引导选项
- ✅ 系统图标显示
- ✅ 清晰的视觉层次

### 2. 主题系统
- ✅ 深色主题（默认）
- ✅ 浅色主题
- ✅ E键实时切换

### 3. 多语言支持
- ✅ 中文界面（默认）
- ✅ 英文界面
- ✅ L键动态切换

### 4. 设置管理
- ✅ TAB键访问设置
- ✅ 主题状态显示
- ✅ 语言状态显示
- ✅ 系统信息展示

## 🔧 部署到真机

### 准备EFI文件
构建完成后，在 `bin/` 目录找到 `bootmgr.efi` 文件。

### 方法1：替换默认引导
```bash
# 备份原有引导文件
sudo cp /boot/efi/EFI/Boot/bootx64.efi /boot/efi/EFI/Boot/bootx64.efi.bak

# 安装空旷系统引导管理器
sudo cp bin/bootmgr.efi /boot/efi/EFI/Boot/bootx64.efi
```

### 方法2：创建独立引导项
```bash
# 创建专用目录
sudo mkdir -p /boot/efi/EFI/SpaciosSystem

# 复制引导文件
sudo cp bin/bootmgr.efi /boot/efi/EFI/SpaciosSystem/bootmgr.efi

# 使用 efibootmgr 创建引导项
sudo efibootmgr -c -d /dev/sda -p 1 -L "空旷系统" -l "\EFI\SpaciosSystem\bootmgr.efi"
```

### 方法3：USB启动盘
```bash
# 格式化USB为FAT32
sudo mkfs.fat -F32 /dev/sdX1

# 挂载USB
sudo mount /dev/sdX1 /mnt

# 创建EFI结构
sudo mkdir -p /mnt/EFI/Boot

# 复制引导文件
sudo cp bin/bootmgr.efi /mnt/EFI/Boot/bootx64.efi

# 卸载
sudo umount /mnt
```

## 🛠️ 自定义配置

### 修改系统名称
编辑 `config.h` 文件：
```cpp
#define SYSTEM_NAME L"您的系统名称"
```

### 修改默认超时
```cpp
#define DEFAULT_TIMEOUT_SECONDS 15  // 改为15秒
```

### 添加自定义引导选项
编辑 `src/boot_manager.cpp` 中的 `ScanBootOptions()` 方法：
```cpp
AddBootOption(L"我的系统", L"My Custom System", NULL);
```

### 修改主题颜色
编辑 `config.h` 中的颜色定义：
```cpp
#define DARK_SELECTED_COLOR     EFI_GREEN  // 改为绿色高亮
```

## 🐛 故障排除

### 构建失败
1. 确认安装了 GCC 和 GNU-EFI
2. 检查 Makefile 中的路径设置
3. 运行 `build.bat check` 检查依赖

### 启动失败
1. 确认系统支持 UEFI 启动
2. 检查 Secure Boot 设置
3. 验证 EFI 文件完整性

### 显示异常
1. 检查终端字符编码设置
2. 确认字体支持 Unicode 字符
3. 尝试不同的主题模式

## 📞 获取帮助

- 查看 `README.md` 获取详细文档
- 查看 `demo.md` 了解功能演示
- 查看 `PROJECT_SUMMARY.md` 了解技术细节

## 🎉 享受体验！

现在您已经成功体验了空旷系统 EFI 引导管理器的所有核心功能。这个现代化的引导管理器为您提供了：

- 🎨 美观的现代界面
- 🌓 灵活的主题系统  
- 🌍 完整的多语言支持
- ⚙️ 便捷的设置管理

开始探索和自定义您的引导体验吧！
