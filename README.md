# 空旷系统 EFI 引导管理器

一个用C++编写的现代化EFI引导管理器，名为"空旷系统"，提供类似现代UEFI引导界面的功能和体验。

## 🌟 功能特性

- 🎨 **现代化界面**: 卡片式引导选项显示，类似现代UEFI界面
- 🌓 **主题切换**: 支持深色/浅色主题实时切换 (E键)
- 🌍 **多语言支持**: 中文/英文界面动态切换 (L键)
- ⚙️ **设置面板**: 完整的设置界面 (TAB键)
- 🔍 **智能扫描**: 自动检测和识别各种操作系统
- ⌨️ **键盘导航**: 完整的键盘操作支持
- ⏱️ **自动启动**: 可配置的倒计时自动启动
- 🎯 **UEFI兼容**: 完全符合UEFI标准

## 系统要求

### 开发环境
- GCC/G++ 编译器 (推荐 MinGW-w64 for Windows)
- GNU-EFI 开发库
- Make 工具
- UEFI固件支持的系统

### 运行环境
- UEFI固件 (不支持传统BIOS)
- x86_64架构处理器

## 安装依赖

### Windows (推荐使用MSYS2)
```bash
# 安装MSYS2后，在MSYS2终端中执行：
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-gnu-efi
pacman -S make
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install build-essential gnu-efi
```

### Fedora/RHEL
```bash
sudo dnf install gcc gcc-c++ gnu-efi-devel
```

### Arch Linux
```bash
sudo pacman -S base-devel gnu-efi
```

## 编译构建

### 使用构建脚本 (推荐)
```bash
# Windows
build.bat

# Linux/macOS
./build.sh
```

### 手动构建
```bash
make all
```

### 清理构建文件
```bash
make clean
```

## 安装部署

### 方法1: 安装到EFI系统分区
```bash
# Linux
sudo make install

# Windows (需要管理员权限)
# 手动复制 bin/bootmgr.efi 到 C:\EFI\Boot\bootx64.efi
```

### 方法2: 创建可启动USB
```bash
# Linux
sudo make usb DEVICE=/dev/sdX

# Windows
# 使用Rufus等工具创建UEFI启动盘，然后复制EFI文件
```

### 方法3: 手动安装
1. 将 `bin/bootmgr.efi` 复制到EFI系统分区
2. 重命名为 `bootx64.efi` 或创建UEFI启动项

## 🚀 使用说明

### 现代化启动界面
启动后会显示现代化的"空旷系统"引导界面：
- 卡片式引导选项显示，支持系统图标
- 实时倒计时显示
- 双语界面支持

### ⌨️ 键盘操作
#### 基本操作
- `↑/↓` - 选择引导选项
- `Enter` - 启动选中的选项
- `ESC` - 退出引导管理器

#### 高级功能
- `E` - 切换深色/浅色主题
- `L` - 切换中文/英文语言
- `TAB` - 显示/隐藏设置面板

### 🎨 主题系统
- **深色主题**: 黑色背景，适合低光环境
- **浅色主题**: 浅色背景，适合明亮环境
- 实时切换，设置会被记住

### 🌍 语言支持
- **中文界面**: 完整的中文本地化
- **英文界面**: 标准英文界面
- 动态切换，无需重启

## 配置选项

### 超时设置
默认超时时间为30秒，可以通过代码修改：
```cpp
mDefaultTimeout = 30; // 秒
```

### 添加自定义引导选项
在 `BootManager::ScanBootOptions()` 中添加：
```cpp
AddBootOption(L"Custom OS", L"My Custom Operating System", NULL);
```

## 开发说明

### 项目结构
```
├── src/
│   ├── efi_main.cpp      # EFI应用程序入口点
│   ├── boot_manager.h    # 引导管理器头文件
│   └── boot_manager.cpp  # 引导管理器实现
├── Makefile              # 构建配置
├── build.sh              # Linux构建脚本
├── build.bat             # Windows构建脚本
└── README.md             # 项目文档
```

### 扩展功能
要添加新功能，可以：
1. 在 `boot_manager.h` 中声明新方法
2. 在 `boot_manager.cpp` 中实现
3. 在 `efi_main.cpp` 中调用

### 调试
```bash
# 生成调试信息
make debug

# 查看汇编代码
cat bin/bootmgr.asm

# 查看ELF信息
cat bin/bootmgr.readelf
```

## 测试

### QEMU测试
```bash
# 安装QEMU和OVMF固件
qemu-system-x86_64 -bios OVMF.fd -drive format=raw,file=fat:rw:bin
```

### VirtualBox测试
1. 创建新虚拟机，启用EFI
2. 将 `bootmgr.efi` 复制到虚拟硬盘的EFI分区
3. 启动虚拟机

## 故障排除

### 编译错误
- 确保安装了GNU-EFI开发包
- 检查Makefile中的路径设置
- 验证GCC版本兼容性

### 启动问题
- 确认系统支持UEFI启动
- 检查Secure Boot设置
- 验证EFI文件完整性

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 致谢

- GNU-EFI项目提供的UEFI开发框架
- Microsoft Windows Boot Manager的界面设计灵感
