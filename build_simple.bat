@echo off
setlocal

echo ===================================
echo Spacious System EFI Boot Manager
echo Simple Build Script
echo ===================================

set "COMMAND=%1"
if "%COMMAND%"=="" set "COMMAND=build"

if "%COMMAND%"=="build" goto :build
if "%COMMAND%"=="clean" goto :clean
if "%COMMAND%"=="help" goto :help
goto :unknown

:build
echo Building project...

echo Checking for GCC...
gcc --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: GCC not found!
    echo Please install MinGW-w64 or similar GCC toolchain.
    echo Download from: https://www.mingw-w64.org/
    goto :end
)

echo Checking for Make...
make --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Make not found!
    echo Please install Make utility with MinGW-w64 or MSYS2.
    goto :end
)

echo Creating directories...
if not exist obj mkdir obj
if not exist bin mkdir bin

echo Compiling source files...
make all
if errorlevel 1 (
    echo BUILD FAILED!
    goto :end
)

echo BUILD SUCCESSFUL!
echo Output: bin\bootmgr.efi

if exist bin\bootmgr.efi (
    for %%A in (bin\bootmgr.efi) do echo File size: %%~zA bytes
)

echo.
echo Installation:
echo 1. Copy bin\bootmgr.efi to EFI system partition
echo 2. Rename to bootx64.efi or create UEFI boot entry
echo.
echo Testing:
echo Run: test.bat
goto :end

:clean
echo Cleaning build files...
if exist obj rmdir /s /q obj
if exist bin rmdir /s /q bin
echo Clean completed.
goto :end

:help
echo Usage: %0 [command]
echo.
echo Commands:
echo   build   - Build the project (default)
echo   clean   - Clean build files  
echo   help    - Show this help
echo.
echo Requirements:
echo - MinGW-w64 GCC toolchain
echo - GNU-EFI development libraries
echo - Make utility
goto :end

:unknown
echo Unknown command: %COMMAND%
echo Use '%0 help' for usage information.
goto :end

:end
endlocal
