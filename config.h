#ifndef CONFIG_H
#define CONFIG_H

// EFI Boot Manager 配置文件

// 版本信息
#define BOOTMGR_VERSION_MAJOR 1
#define BOOTMGR_VERSION_MINOR 0
#define BOOTMGR_VERSION_PATCH 0
#define BOOTMGR_VERSION_STRING L"1.0.0"

// 系统信息
#define SYSTEM_NAME L"空旷系统"
#define SYSTEM_SUBTITLE L"Select Your System"

// 默认配置
#define DEFAULT_TIMEOUT_SECONDS 30
#define MAX_BOOT_OPTIONS 16
#define MAX_NAME_LENGTH 256
#define MAX_DESCRIPTION_LENGTH 512

// 主题配置
typedef enum {
    THEME_DARK = 0,
    THEME_LIGHT = 1
} THEME_MODE;

// 语言配置
typedef enum {
    LANG_CHINESE = 0,
    LANG_ENGLISH = 1
} LANGUAGE_MODE;

// 界面文本 - 中文
#define TITLE_TEXT_CN L"空旷系统"
#define SUBTITLE_TEXT_CN L"选择您的系统"
#define INSTRUCTION_TEXT_CN L"使用方向键选择，回车确认"
#define TIMEOUT_TEXT_CN L"自动启动倒计时: %d 秒"
#define SETTINGS_TEXT_CN L"E - 设置  L - 语言"
#define THEME_DARK_CN L"深色主题"
#define THEME_LIGHT_CN L"浅色主题"

// 界面文本 - 英文
#define TITLE_TEXT_EN L"Spacious System"
#define SUBTITLE_TEXT_EN L"Select Your System"
#define INSTRUCTION_TEXT_EN L"Use arrow keys to select, press ENTER to confirm"
#define TIMEOUT_TEXT_EN L"Auto boot countdown: %d seconds"
#define SETTINGS_TEXT_EN L"E - Settings  L - Language"
#define THEME_DARK_EN L"Dark Theme"
#define THEME_LIGHT_EN L"Light Theme"

// 默认引导选项
#define DEFAULT_BOOT_OPTIONS { \
    {L"Windows Boot Manager", L"Microsoft Windows"}, \
    {L"UEFI Shell", L"EFI Shell Environment"}, \
    {L"Setup", L"UEFI Setup Utility"} \
}

// 扫描路径配置
#define SCAN_PATHS { \
    L"\\EFI\\Microsoft\\Boot\\bootmgfw.efi", \
    L"\\EFI\\Boot\\bootx64.efi", \
    L"\\EFI\\Boot\\grubx64.efi", \
    L"\\EFI\\ubuntu\\grubx64.efi", \
    L"\\EFI\\fedora\\grubx64.efi", \
    L"\\EFI\\opensuse\\grubx64.efi" \
}

// 深色主题颜色配置
#define DARK_BG_COLOR           EFI_BLACK
#define DARK_TEXT_COLOR         EFI_LIGHTGRAY
#define DARK_SELECTED_COLOR     EFI_BLUE
#define DARK_TITLE_COLOR        EFI_WHITE
#define DARK_ACCENT_COLOR       EFI_CYAN

// 浅色主题颜色配置
#define LIGHT_BG_COLOR          EFI_LIGHTGRAY
#define LIGHT_TEXT_COLOR        EFI_BLACK
#define LIGHT_SELECTED_COLOR    EFI_BLUE
#define LIGHT_TITLE_COLOR       EFI_DARKGRAY
#define LIGHT_ACCENT_COLOR      EFI_BLUE

// 调试配置
#ifdef DEBUG
#define DEBUG_PRINT(fmt, ...) Print(L"[DEBUG] " fmt, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...)
#endif

// 功能开关
#define ENABLE_TIMEOUT          1
#define ENABLE_KEYBOARD_NAV     1
#define ENABLE_AUTO_SCAN        1
#define ENABLE_CONFIG_SAVE      1
#define ENABLE_THEME_SWITCH     1  // 主题切换
#define ENABLE_LANGUAGE_SWITCH  1  // 语言切换
#define ENABLE_MODERN_UI        1  // 现代化界面
#define ENABLE_TOOLS_MENU       0  // 暂未实现
#define ENABLE_TROUBLESHOOT     0  // 暂未实现

// 文件系统配置
#define MAX_FILESYSTEMS         8
#define MAX_PATH_LENGTH         1024

// 内存配置
#define INITIAL_POOL_SIZE       4096
#define MAX_POOL_SIZE          65536

#endif // CONFIG_H
