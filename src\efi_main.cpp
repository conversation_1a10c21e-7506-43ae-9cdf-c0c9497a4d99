#include <efi.h>
#include <efilib.h>
#include "boot_manager.h"
#include "../config.h"

// EFI应用程序入口点
EFI_STATUS EFIAPI efi_main(EFI_HANDLE ImageHandle, EFI_SYSTEM_TABLE *SystemTable)
{
    EFI_STATUS Status;
    
    // 初始化EFI库
    InitializeLib(ImageHandle, SystemTable);
    
    // 清屏
    SystemTable->ConOut->ClearScreen(SystemTable->ConOut);
    
    // 显示启动信息
    Print(L"%s\n", SYSTEM_NAME);
    Print(L"Version %s\n", BOOTMGR_VERSION_STRING);
    Print(L"=============================\n\n");
    
    // 初始化引导管理器
    BootManager bootManager;
    Status = bootManager.Initialize(SystemTable);
    if (EFI_ERROR(Status)) {
        Print(L"Failed to initialize boot manager: %r\n", Status);
        return Status;
    }
    
    // 扫描引导选项
    Status = bootManager.ScanBootOptions();
    if (EFI_ERROR(Status)) {
        Print(L"Failed to scan boot options: %r\n", Status);
        return Status;
    }
    
    // 显示引导菜单
    Status = bootManager.ShowBootMenu();
    if (EFI_ERROR(Status)) {
        Print(L"Failed to show boot menu: %r\n", Status);
        return Status;
    }
    
    // 处理用户选择
    Status = bootManager.ProcessUserSelection();
    if (EFI_ERROR(Status)) {
        Print(L"Failed to process user selection: %r\n", Status);
        return Status;
    }
    
    return EFI_SUCCESS;
}
