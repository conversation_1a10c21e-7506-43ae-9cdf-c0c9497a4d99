# Windows-like EFI Boot Manager

一个用C++编写的类似Windows系统的EFI引导管理器，提供类似Windows Boot Manager的功能和界面。

## 功能特性

- 🚀 类似Windows Boot Manager的用户界面
- 🔍 自动扫描和检测引导选项
- ⌨️ 键盘导航支持（方向键、回车、ESC等）
- ⏱️ 可配置的自动启动超时
- 🛠️ 引导选项管理
- 📁 支持多种引导文件格式
- 🎯 UEFI标准兼容

## 系统要求

### 开发环境
- GCC/G++ 编译器 (推荐 MinGW-w64 for Windows)
- GNU-EFI 开发库
- Make 工具
- UEFI固件支持的系统

### 运行环境
- UEFI固件 (不支持传统BIOS)
- x86_64架构处理器

## 安装依赖

### Windows (推荐使用MSYS2)
```bash
# 安装MSYS2后，在MSYS2终端中执行：
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-gnu-efi
pacman -S make
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install build-essential gnu-efi
```

### Fedora/RHEL
```bash
sudo dnf install gcc gcc-c++ gnu-efi-devel
```

### Arch Linux
```bash
sudo pacman -S base-devel gnu-efi
```

## 编译构建

### 使用构建脚本 (推荐)
```bash
# Windows
build.bat

# Linux/macOS
./build.sh
```

### 手动构建
```bash
make all
```

### 清理构建文件
```bash
make clean
```

## 安装部署

### 方法1: 安装到EFI系统分区
```bash
# Linux
sudo make install

# Windows (需要管理员权限)
# 手动复制 bin/bootmgr.efi 到 C:\EFI\Boot\bootx64.efi
```

### 方法2: 创建可启动USB
```bash
# Linux
sudo make usb DEVICE=/dev/sdX

# Windows
# 使用Rufus等工具创建UEFI启动盘，然后复制EFI文件
```

### 方法3: 手动安装
1. 将 `bin/bootmgr.efi` 复制到EFI系统分区
2. 重命名为 `bootx64.efi` 或创建UEFI启动项

## 使用说明

### 启动界面
启动后会显示类似Windows Boot Manager的界面：
- 使用 ↑↓ 方向键选择引导选项
- 按 Enter 启动选中的选项
- 按 ESC 取消启动
- 按 TAB 访问工具菜单（开发中）

### 键盘快捷键
- `↑/↓` - 选择引导选项
- `Enter` - 启动选中的选项
- `ESC` - 取消启动
- `TAB` - 工具菜单
- `F8` - 故障排除（开发中）

## 配置选项

### 超时设置
默认超时时间为30秒，可以通过代码修改：
```cpp
mDefaultTimeout = 30; // 秒
```

### 添加自定义引导选项
在 `BootManager::ScanBootOptions()` 中添加：
```cpp
AddBootOption(L"Custom OS", L"My Custom Operating System", NULL);
```

## 开发说明

### 项目结构
```
├── src/
│   ├── efi_main.cpp      # EFI应用程序入口点
│   ├── boot_manager.h    # 引导管理器头文件
│   └── boot_manager.cpp  # 引导管理器实现
├── Makefile              # 构建配置
├── build.sh              # Linux构建脚本
├── build.bat             # Windows构建脚本
└── README.md             # 项目文档
```

### 扩展功能
要添加新功能，可以：
1. 在 `boot_manager.h` 中声明新方法
2. 在 `boot_manager.cpp` 中实现
3. 在 `efi_main.cpp` 中调用

### 调试
```bash
# 生成调试信息
make debug

# 查看汇编代码
cat bin/bootmgr.asm

# 查看ELF信息
cat bin/bootmgr.readelf
```

## 测试

### QEMU测试
```bash
# 安装QEMU和OVMF固件
qemu-system-x86_64 -bios OVMF.fd -drive format=raw,file=fat:rw:bin
```

### VirtualBox测试
1. 创建新虚拟机，启用EFI
2. 将 `bootmgr.efi` 复制到虚拟硬盘的EFI分区
3. 启动虚拟机

## 故障排除

### 编译错误
- 确保安装了GNU-EFI开发包
- 检查Makefile中的路径设置
- 验证GCC版本兼容性

### 启动问题
- 确认系统支持UEFI启动
- 检查Secure Boot设置
- 验证EFI文件完整性

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 致谢

- GNU-EFI项目提供的UEFI开发框架
- Microsoft Windows Boot Manager的界面设计灵感
