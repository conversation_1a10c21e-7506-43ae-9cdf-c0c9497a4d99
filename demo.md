# 空旷系统 EFI 引导管理器演示

## 新功能展示

### 🎨 现代化界面
- 类似图片中的现代UEFI引导界面
- 卡片式引导选项显示
- 清晰的系统图标和描述

### 🌓 主题切换 (E键)
- **深色主题**: 黑色背景，浅色文字
- **浅色主题**: 浅色背景，深色文字
- 实时切换，无需重启

### 🌍 语言切换 (L键)
- **中文界面**: 完整的中文本地化
- **英文界面**: 英文界面支持
- 动态语言切换

### ⚙️ 设置面板 (TAB键)
- 主题设置查看和切换
- 语言设置查看和切换
- 系统信息显示
- 版本和配置信息

## 界面预览

### 主界面 (深色主题 - 中文)
```
                    空旷系统
                 选择您的系统

  ┌─────────────────────────────────────────────────────────────┐
  │                                                             │
  │  ▶ ◆  空旷系统 20.0                                         │
  │      Spacious System 20.0                                   │
  │    🐧  Manjaro Linux 18.0                                   │
  │      Manjaro Linux Distribution                             │
  │    🐧  Ubuntu 24.01                                         │
  │      Ubuntu Linux Distribution                              │
  │    ⊞  Windows 11                                            │
  │      Microsoft Windows 11                                   │
  │    🐧  Fedora 42.0                                          │
  │      Fedora Linux Distribution                              │
  │    ⚙  UEFI Shell                                            │
  │      EFI Shell Environment                                  │
  │                                                             │
  └─────────────────────────────────────────────────────────────┘

                 自动启动倒计时: 30 秒

              E - 设置  L - 语言
         ENTER=启动  ESC=退出  ↑↓=选择
```

### 主界面 (浅色主题 - 英文)
```
                    Spacious System
                 Select Your System

  ┌─────────────────────────────────────────────────────────────┐
  │                                                             │
  │  ▶ ◆  Spacious System 20.0                                 │
  │      Spacious System 20.0                                   │
  │    🐧  Manjaro Linux 18.0                                   │
  │      Manjaro Linux Distribution                             │
  │    🐧  Ubuntu 24.01                                         │
  │      Ubuntu Linux Distribution                              │
  │    ⊞  Windows 11                                            │
  │      Microsoft Windows 11                                   │
  │    🐧  Fedora 42.0                                          │
  │      Fedora Linux Distribution                              │
  │    ⚙  UEFI Shell                                            │
  │      EFI Shell Environment                                  │
  │                                                             │
  └─────────────────────────────────────────────────────────────┘

                 Auto boot countdown: 30 seconds

              E - Settings  L - Language
         ENTER=Boot  ESC=Exit  ↑↓=Select
```

### 设置面板
```
                    设置
  ═══════════════════════════════════════════════════════════

  主题模式: 深色主题
  按 E 键切换主题 / Press E to toggle theme

  语言: 中文
  按 L 键切换语言 / Press L to toggle language

  系统信息:
  ─────────────────────────────────────────────────────────
  系统名称 / System Name: 空旷系统
  版本 / Version: 1.0.0
  引导选项数量 / Boot Options: 6
  当前主题 / Current Theme: 深色主题
  当前语言 / Current Language: 中文

  按 TAB 返回主界面 / Press TAB to return to main menu
  按 ESC 退出 / Press ESC to exit
```

## 操作说明

### 基本操作
- **↑/↓ 方向键**: 选择引导选项
- **ENTER**: 启动选中的系统
- **ESC**: 退出引导管理器

### 高级功能
- **E键**: 切换深色/浅色主题
- **L键**: 切换中文/英文语言
- **TAB键**: 显示/隐藏设置面板

### 自动功能
- **30秒倒计时**: 自动启动默认选项
- **主题记忆**: 记住用户选择的主题
- **语言记忆**: 记住用户选择的语言

## 系统图标说明
- **◆**: 空旷系统 (Spacious System)
- **🐧**: Linux 发行版
- **⊞**: Windows 系统
- **⚙**: 系统工具和Shell
- **▲**: 其他系统

## 技术特性
- 完全UEFI兼容
- 支持x86_64架构
- 实时主题切换
- 动态语言本地化
- 现代化卡片式界面
- 响应式用户交互

## 构建和测试
```bash
# Windows
build.bat

# 测试
test.bat

# Linux
./build.sh
```

生成的 `bin/bootmgr.efi` 文件可以直接部署到EFI系统分区使用。
