#ifndef BOOT_MANAGER_H
#define BOOT_MANAGER_H

#include <efi.h>
#include <efilib.h>
#include "../config.h"

// 引导选项结构
typedef struct {
    CHAR16 *Name;           // 引导选项名称
    CHAR16 *Description;    // 描述
    EFI_DEVICE_PATH *DevicePath;  // 设备路径
    EFI_HANDLE ImageHandle; // 镜像句柄
    BOOLEAN IsDefault;      // 是否为默认选项
    BOOLEAN IsActive;       // 是否激活
} BOOT_OPTION;

// 引导管理器类
class BootManager {
private:
    EFI_SYSTEM_TABLE *mSystemTable;
    EFI_BOOT_SERVICES *mBootServices;
    EFI_RUNTIME_SERVICES *mRuntimeServices;
    
    BOOT_OPTION *mBootOptions;
    UINTN mBootOptionCount;
    UINTN mSelectedOption;
    UINTN mDefaultTimeout;  // 默认超时时间（秒）
    
    // 私有方法
    EFI_STATUS ScanFileSystem();
    EFI_STATUS LoadBootOption(EFI_HANDLE Handle, CHAR16 *Path);
    EFI_STATUS AddBootOption(CHAR16 *Name, CHAR16 *Description, EFI_DEVICE_PATH *DevicePath);
    EFI_STATUS GetUserInput();
    EFI_STATUS LaunchBootOption(UINTN Index);
    VOID DisplayBootOption(UINTN Index, BOOLEAN Selected);
    VOID RefreshScreen();
    
public:
    BootManager();
    ~BootManager();
    
    // 公共方法
    EFI_STATUS Initialize(EFI_SYSTEM_TABLE *SystemTable);
    EFI_STATUS ScanBootOptions();
    EFI_STATUS ShowBootMenu();
    EFI_STATUS ProcessUserSelection();
    EFI_STATUS SetDefaultOption(UINTN Index);
    EFI_STATUS SetTimeout(UINTN Seconds);
    
    // 配置管理
    EFI_STATUS LoadConfiguration();
    EFI_STATUS SaveConfiguration();
};

#endif // BOOT_MANAGER_H
