# EFI Boot Manager Makefile
# 需要安装 GNU-EFI 开发包

# 编译器和工具
CC = gcc
CXX = g++
LD = ld
OBJCOPY = objcopy

# 目标架构
ARCH = x86_64
EFI_ARCH = x64

# GNU-EFI 路径 (需要根据实际安装路径调整)
GNUEFI_DIR = /usr/include/efi
GNUEFI_LIB_DIR = /usr/lib

# 编译标志
CFLAGS = -I$(GNUEFI_DIR) \
         -I$(GNUEFI_DIR)/$(ARCH) \
         -I$(GNUEFI_DIR)/protocol \
         -fno-stack-protector \
         -fpic \
         -fshort-wchar \
         -mno-red-zone \
         -Wall \
         -DEFI_FUNCTION_WRAPPER \
         -DGNU_EFI_USE_MS_ABI

CXXFLAGS = $(CFLAGS) -fno-exceptions -fno-rtti

# 链接标志
LDFLAGS = -nostdlib \
          -znocombreloc \
          -T $(GNUEFI_LIB_DIR)/elf_$(ARCH)_efi.lds \
          -shared \
          -Bsymbolic \
          -L $(GNUEFI_LIB_DIR) \
          $(GNUEFI_LIB_DIR)/crt0-efi-$(ARCH).o

LIBS = -lefi -lgnuefi

# 源文件
SRCDIR = src
OBJDIR = obj
BINDIR = bin

SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)

# 目标文件
TARGET = bootmgr
EFI_TARGET = $(BINDIR)/$(TARGET).efi
SO_TARGET = $(BINDIR)/$(TARGET).so

# 默认目标
all: directories $(EFI_TARGET)

# 创建目录
directories:
	@mkdir -p $(OBJDIR) $(BINDIR)

# 编译C++源文件
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

# 链接生成共享对象
$(SO_TARGET): $(OBJECTS)
	$(LD) $(LDFLAGS) $(OBJECTS) -o $@ $(LIBS)

# 转换为EFI可执行文件
$(EFI_TARGET): $(SO_TARGET)
	$(OBJCOPY) -j .text -j .sdata -j .data -j .dynamic \
	           -j .dynsym -j .rel -j .rela -j .reloc \
	           --target=efi-app-$(ARCH) $< $@

# 清理
clean:
	rm -rf $(OBJDIR) $(BINDIR)

# 安装到EFI系统分区 (需要管理员权限)
install: $(EFI_TARGET)
	@echo "Installing to EFI System Partition..."
	@echo "This requires administrator privileges"
	sudo mkdir -p /boot/efi/EFI/BootMgr
	sudo cp $(EFI_TARGET) /boot/efi/EFI/BootMgr/bootmgr.efi

# 创建可启动USB (需要指定设备)
usb: $(EFI_TARGET)
	@echo "Usage: make usb DEVICE=/dev/sdX"
	@echo "This will create a bootable USB drive"
ifdef DEVICE
	sudo mkfs.fat -F32 $(DEVICE)1
	sudo mkdir -p /mnt/usb
	sudo mount $(DEVICE)1 /mnt/usb
	sudo mkdir -p /mnt/usb/EFI/Boot
	sudo cp $(EFI_TARGET) /mnt/usb/EFI/Boot/bootx64.efi
	sudo umount /mnt/usb
	@echo "Bootable USB created successfully"
else
	@echo "Please specify DEVICE=/dev/sdX"
endif

# 调试信息
debug: $(SO_TARGET)
	objdump -d $(SO_TARGET) > $(BINDIR)/$(TARGET).asm
	readelf -a $(SO_TARGET) > $(BINDIR)/$(TARGET).readelf

# 帮助
help:
	@echo "Available targets:"
	@echo "  all      - Build the EFI application"
	@echo "  clean    - Remove build files"
	@echo "  install  - Install to EFI System Partition"
	@echo "  usb      - Create bootable USB (specify DEVICE=/dev/sdX)"
	@echo "  debug    - Generate debug information"
	@echo "  help     - Show this help"

.PHONY: all clean install usb debug help directories
