#ifndef CONFIG_H
#define CONFIG_H

// EFI Boot Manager 配置文件

// 版本信息
#define BOOTMGR_VERSION_MAJOR 1
#define BOOTMGR_VERSION_MINOR 0
#define BOOTMGR_VERSION_PATCH 0
#define BOOTMGR_VERSION_STRING L"1.0.0"

// 默认配置
#define DEFAULT_TIMEOUT_SECONDS 30
#define MAX_BOOT_OPTIONS 16
#define MAX_NAME_LENGTH 256
#define MAX_DESCRIPTION_LENGTH 512

// 界面配置
#define TITLE_TEXT L"Windows-like Boot Manager"
#define SUBTITLE_TEXT L"Choose an operating system to start, or press TAB to select a tool:"
#define INSTRUCTION_TEXT L"(Use the arrow keys to highlight your choice, then press ENTER.)"
#define TIMEOUT_TEXT L"Seconds until the highlighted choice will be started automatically: %d"
#define TOOLS_TITLE L"Tools:"
#define HELP_TEXT L"ENTER=Choose   TAB=Menu   ESC=Cancel   F8=Troubleshoot"

// 默认引导选项
#define DEFAULT_BOOT_OPTIONS { \
    {L"Windows Boot Manager", L"Microsoft Windows"}, \
    {L"UEFI Shell", L"EFI Shell Environment"}, \
    {L"Setup", L"UEFI Setup Utility"} \
}

// 扫描路径配置
#define SCAN_PATHS { \
    L"\\EFI\\Microsoft\\Boot\\bootmgfw.efi", \
    L"\\EFI\\Boot\\bootx64.efi", \
    L"\\EFI\\Boot\\grubx64.efi", \
    L"\\EFI\\ubuntu\\grubx64.efi", \
    L"\\EFI\\fedora\\grubx64.efi", \
    L"\\EFI\\opensuse\\grubx64.efi" \
}

// 颜色配置 (如果支持)
#define COLOR_NORMAL     EFI_LIGHTGRAY
#define COLOR_SELECTED   EFI_WHITE
#define COLOR_TITLE      EFI_YELLOW
#define COLOR_ERROR      EFI_LIGHTRED
#define COLOR_SUCCESS    EFI_LIGHTGREEN

// 调试配置
#ifdef DEBUG
#define DEBUG_PRINT(fmt, ...) Print(L"[DEBUG] " fmt, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...)
#endif

// 功能开关
#define ENABLE_TIMEOUT          1
#define ENABLE_KEYBOARD_NAV     1
#define ENABLE_AUTO_SCAN        1
#define ENABLE_CONFIG_SAVE      1
#define ENABLE_TOOLS_MENU       0  // 暂未实现
#define ENABLE_TROUBLESHOOT     0  // 暂未实现

// 文件系统配置
#define MAX_FILESYSTEMS         8
#define MAX_PATH_LENGTH         1024

// 内存配置
#define INITIAL_POOL_SIZE       4096
#define MAX_POOL_SIZE          65536

#endif // CONFIG_H
