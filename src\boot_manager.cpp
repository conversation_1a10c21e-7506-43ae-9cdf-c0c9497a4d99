#include "boot_manager.h"

BootManager::BootManager() {
    mSystemTable = NULL;
    mBootServices = NULL;
    mRuntimeServices = NULL;
    mBootOptions = NULL;
    mBootOptionCount = 0;
    mSelectedOption = 0;
    mDefaultTimeout = 30; // 默认30秒超时
}

BootManager::~BootManager() {
    if (mBootOptions) {
        for (UINTN i = 0; i < mBootOptionCount; i++) {
            if (mBootOptions[i].Name) {
                FreePool(mBootOptions[i].Name);
            }
            if (mBootOptions[i].Description) {
                FreePool(mBootOptions[i].Description);
            }
        }
        FreePool(mBootOptions);
    }
}

EFI_STATUS BootManager::Initialize(EFI_SYSTEM_TABLE *SystemTable) {
    if (!SystemTable) {
        return EFI_INVALID_PARAMETER;
    }
    
    mSystemTable = SystemTable;
    mBootServices = SystemTable->BootServices;
    mRuntimeServices = SystemTable->RuntimeServices;
    
    // 加载配置
    LoadConfiguration();
    
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::ScanBootOptions() {
    EFI_STATUS Status;
    
    Print(L"Scanning for boot options...\n");
    
    // 扫描文件系统
    Status = ScanFileSystem();
    if (EFI_ERROR(Status)) {
        Print(L"Failed to scan file systems: %r\n", Status);
        return Status;
    }
    
    // 添加一些默认的引导选项
    AddBootOption(L"Windows Boot Manager", L"Microsoft Windows", NULL);
    AddBootOption(L"UEFI Shell", L"EFI Shell Environment", NULL);
    AddBootOption(L"Setup", L"UEFI Setup Utility", NULL);
    
    Print(L"Found %d boot options\n", mBootOptionCount);
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::ScanFileSystem() {
    EFI_STATUS Status;
    UINTN HandleCount;
    EFI_HANDLE *HandleBuffer;
    
    // 获取所有简单文件系统协议句柄
    Status = mBootServices->LocateHandleBuffer(
        ByProtocol,
        &gEfiSimpleFileSystemProtocolGuid,
        NULL,
        &HandleCount,
        &HandleBuffer
    );
    
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    // 遍历每个文件系统
    for (UINTN i = 0; i < HandleCount; i++) {
        // 检查是否有bootmgfw.efi或其他引导文件
        LoadBootOption(HandleBuffer[i], L"\\EFI\\Microsoft\\Boot\\bootmgfw.efi");
        LoadBootOption(HandleBuffer[i], L"\\EFI\\Boot\\bootx64.efi");
    }
    
    if (HandleBuffer) {
        FreePool(HandleBuffer);
    }
    
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::LoadBootOption(EFI_HANDLE Handle, CHAR16 *Path) {
    // 这里应该检查文件是否存在，并创建相应的引导选项
    // 简化实现，实际应该打开文件系统并检查文件
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::AddBootOption(CHAR16 *Name, CHAR16 *Description, EFI_DEVICE_PATH *DevicePath) {
    EFI_STATUS Status;
    BOOT_OPTION *NewOptions;
    
    // 重新分配内存
    Status = mBootServices->AllocatePool(
        EfiBootServicesData,
        (mBootOptionCount + 1) * sizeof(BOOT_OPTION),
        (VOID**)&NewOptions
    );
    
    if (EFI_ERROR(Status)) {
        return Status;
    }
    
    // 复制现有选项
    if (mBootOptions) {
        CopyMem(NewOptions, mBootOptions, mBootOptionCount * sizeof(BOOT_OPTION));
        FreePool(mBootOptions);
    }
    
    mBootOptions = NewOptions;
    
    // 添加新选项
    BOOT_OPTION *NewOption = &mBootOptions[mBootOptionCount];
    
    // 分配并复制名称
    Status = mBootServices->AllocatePool(
        EfiBootServicesData,
        (StrLen(Name) + 1) * sizeof(CHAR16),
        (VOID**)&NewOption->Name
    );
    if (!EFI_ERROR(Status)) {
        StrCpy(NewOption->Name, Name);
    }
    
    // 分配并复制描述
    Status = mBootServices->AllocatePool(
        EfiBootServicesData,
        (StrLen(Description) + 1) * sizeof(CHAR16),
        (VOID**)&NewOption->Description
    );
    if (!EFI_ERROR(Status)) {
        StrCpy(NewOption->Description, Description);
    }
    
    NewOption->DevicePath = DevicePath;
    NewOption->ImageHandle = NULL;
    NewOption->IsDefault = (mBootOptionCount == 0); // 第一个选项为默认
    NewOption->IsActive = TRUE;
    
    mBootOptionCount++;
    
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::ShowBootMenu() {
    if (mBootOptionCount == 0) {
        Print(L"No boot options found!\n");
        return EFI_NOT_FOUND;
    }

    RefreshScreen();
    return EFI_SUCCESS;
}

VOID BootManager::RefreshScreen() {
    // 清屏
    mSystemTable->ConOut->ClearScreen(mSystemTable->ConOut);

    // 显示标题
    Print(L"Windows-like Boot Manager\n");
    Print(L"=========================\n\n");
    Print(L"Choose an operating system to start, or press TAB to select a tool:\n");
    Print(L"(Use the arrow keys to highlight your choice, then press ENTER.)\n\n");

    // 显示引导选项
    for (UINTN i = 0; i < mBootOptionCount; i++) {
        DisplayBootOption(i, (i == mSelectedOption));
    }

    Print(L"\n");
    Print(L"Seconds until the highlighted choice will be started automatically: %d\n", mDefaultTimeout);
    Print(L"\n");
    Print(L"Tools:\n");
    Print(L"Windows Memory Diagnostic\n");
    Print(L"\n");
    Print(L"ENTER=Choose   TAB=Menu   ESC=Cancel   F8=Troubleshoot\n");
}

VOID BootManager::DisplayBootOption(UINTN Index, BOOLEAN Selected) {
    if (Index >= mBootOptionCount) {
        return;
    }

    BOOT_OPTION *Option = &mBootOptions[Index];

    if (Selected) {
        Print(L"  > %s\n", Option->Name);
        Print(L"      %s\n", Option->Description);
    } else {
        Print(L"    %s\n", Option->Name);
        Print(L"      %s\n", Option->Description);
    }
}

EFI_STATUS BootManager::ProcessUserSelection() {
    EFI_STATUS Status;
    EFI_INPUT_KEY Key;
    UINTN TimeoutCounter = mDefaultTimeout;

    while (TRUE) {
        // 检查是否有按键输入
        Status = mSystemTable->ConIn->ReadKeyStroke(mSystemTable->ConIn, &Key);

        if (Status == EFI_SUCCESS) {
            // 处理按键
            switch (Key.ScanCode) {
                case SCAN_UP:
                    if (mSelectedOption > 0) {
                        mSelectedOption--;
                        RefreshScreen();
                    }
                    break;

                case SCAN_DOWN:
                    if (mSelectedOption < mBootOptionCount - 1) {
                        mSelectedOption++;
                        RefreshScreen();
                    }
                    break;

                case SCAN_ESC:
                    Print(L"Boot cancelled by user.\n");
                    return EFI_ABORTED;

                default:
                    break;
            }

            switch (Key.UnicodeChar) {
                case CHAR_CARRIAGE_RETURN:
                    // 启动选中的引导选项
                    return LaunchBootOption(mSelectedOption);

                case CHAR_TAB:
                    Print(L"Tools menu not implemented yet.\n");
                    break;

                default:
                    break;
            }

            // 重置超时计数器
            TimeoutCounter = mDefaultTimeout;
        } else if (Status == EFI_NOT_READY) {
            // 没有按键，等待1秒
            mBootServices->Stall(1000000); // 1秒 = 1,000,000微秒
            TimeoutCounter--;

            if (TimeoutCounter == 0) {
                // 超时，启动默认选项
                Print(L"Timeout reached, booting default option...\n");
                return LaunchBootOption(mSelectedOption);
            }

            // 更新超时显示
            RefreshScreen();
        } else {
            // 其他错误
            Print(L"Error reading input: %r\n", Status);
            return Status;
        }
    }

    return EFI_SUCCESS;
}

EFI_STATUS BootManager::LaunchBootOption(UINTN Index) {
    if (Index >= mBootOptionCount) {
        return EFI_INVALID_PARAMETER;
    }

    BOOT_OPTION *Option = &mBootOptions[Index];

    Print(L"Launching: %s\n", Option->Name);
    Print(L"Description: %s\n", Option->Description);

    // 这里应该实际启动引导选项
    // 简化实现，实际应该加载并执行EFI应用程序
    Print(L"Boot option launch not fully implemented yet.\n");
    Print(L"This would normally chain-load the selected bootloader.\n");

    // 等待用户按键
    Print(L"Press any key to continue...\n");
    EFI_INPUT_KEY Key;
    while (mSystemTable->ConIn->ReadKeyStroke(mSystemTable->ConIn, &Key) != EFI_SUCCESS) {
        mBootServices->Stall(100000); // 100ms
    }

    return EFI_SUCCESS;
}

EFI_STATUS BootManager::SetDefaultOption(UINTN Index) {
    if (Index >= mBootOptionCount) {
        return EFI_INVALID_PARAMETER;
    }

    // 清除所有默认标记
    for (UINTN i = 0; i < mBootOptionCount; i++) {
        mBootOptions[i].IsDefault = FALSE;
    }

    // 设置新的默认选项
    mBootOptions[Index].IsDefault = TRUE;
    mSelectedOption = Index;

    return EFI_SUCCESS;
}

EFI_STATUS BootManager::SetTimeout(UINTN Seconds) {
    mDefaultTimeout = Seconds;
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::LoadConfiguration() {
    // 简化实现，使用默认配置
    // 实际应该从NVRAM或配置文件加载
    mDefaultTimeout = 30;
    return EFI_SUCCESS;
}

EFI_STATUS BootManager::SaveConfiguration() {
    // 简化实现
    // 实际应该保存到NVRAM或配置文件
    return EFI_SUCCESS;
}
