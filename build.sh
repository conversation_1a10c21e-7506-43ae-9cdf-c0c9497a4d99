#!/bin/bash

# EFI Boot Manager 构建脚本

set -e  # 遇到错误时退出

echo "==================================="
echo "EFI Boot Manager Build Script"
echo "==================================="

# 检查依赖
check_dependencies() {
    echo "Checking dependencies..."
    
    # 检查编译器
    if ! command -v gcc &> /dev/null; then
        echo "Error: gcc not found. Please install build-essential."
        exit 1
    fi
    
    if ! command -v g++ &> /dev/null; then
        echo "Error: g++ not found. Please install build-essential."
        exit 1
    fi
    
    # 检查GNU-EFI
    if [ ! -d "/usr/include/efi" ]; then
        echo "Error: GNU-EFI not found."
        echo "Please install gnu-efi development package:"
        echo "  Ubuntu/Debian: sudo apt install gnu-efi"
        echo "  Fedora/RHEL:   sudo dnf install gnu-efi-devel"
        echo "  Arch Linux:    sudo pacman -S gnu-efi"
        exit 1
    fi
    
    echo "Dependencies check passed."
}

# 清理之前的构建
clean_build() {
    echo "Cleaning previous build..."
    make clean
}

# 构建项目
build_project() {
    echo "Building EFI Boot Manager..."
    make all
    
    if [ $? -eq 0 ]; then
        echo "Build successful!"
        echo "EFI executable: bin/bootmgr.efi"
    else
        echo "Build failed!"
        exit 1
    fi
}

# 显示构建信息
show_info() {
    echo ""
    echo "Build Information:"
    echo "=================="
    
    if [ -f "bin/bootmgr.efi" ]; then
        echo "Output file: bin/bootmgr.efi"
        echo "File size: $(du -h bin/bootmgr.efi | cut -f1)"
        echo "File type: $(file bin/bootmgr.efi)"
    fi
    
    echo ""
    echo "Installation Options:"
    echo "===================="
    echo "1. Install to EFI System Partition:"
    echo "   sudo make install"
    echo ""
    echo "2. Create bootable USB:"
    echo "   sudo make usb DEVICE=/dev/sdX"
    echo ""
    echo "3. Manual installation:"
    echo "   Copy bin/bootmgr.efi to your EFI system partition"
    echo "   Example: /boot/efi/EFI/Boot/bootx64.efi"
}

# 主函数
main() {
    case "${1:-build}" in
        "clean")
            clean_build
            ;;
        "check")
            check_dependencies
            ;;
        "build")
            check_dependencies
            clean_build
            build_project
            show_info
            ;;
        "install")
            check_dependencies
            build_project
            echo "Installing to EFI System Partition..."
            sudo make install
            ;;
        "help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  build   - Build the project (default)"
            echo "  clean   - Clean build files"
            echo "  check   - Check dependencies"
            echo "  install - Build and install to EFI System Partition"
            echo "  help    - Show this help"
            ;;
        *)
            echo "Unknown command: $1"
            echo "Use '$0 help' for usage information."
            exit 1
            ;;
    esac
}

main "$@"
