@echo off
REM EFI Boot Manager 构建脚本 (Windows版本)

setlocal enabledelayedexpansion

echo ===================================
echo EFI Boot Manager Build Script
echo ===================================

REM 检查参数
set "COMMAND=%1"
if "%COMMAND%"=="" set "COMMAND=build"

goto :%COMMAND% 2>nul || goto :unknown_command

:build
echo Building EFI Boot Manager...
call :check_dependencies
if errorlevel 1 exit /b 1

call :clean_build
call :build_project
if errorlevel 1 exit /b 1

call :show_info
goto :end

:clean
echo Cleaning previous build...
if exist obj rmdir /s /q obj
if exist bin rmdir /s /q bin
echo Clean completed.
goto :end

:check
call :check_dependencies
goto :end

:help
echo Usage: %0 [command]
echo.
echo Commands:
echo   build   - Build the project (default)
echo   clean   - Clean build files
echo   check   - Check dependencies
echo   help    - Show this help
echo.
echo Note: This script requires MinGW-w64 or similar GCC toolchain
echo       and GNU-EFI development libraries.
goto :end

:check_dependencies
echo Checking dependencies...

REM 检查GCC
gcc --version >nul 2>&1
if errorlevel 1 (
    echo Error: gcc not found. Please install MinGW-w64 or similar GCC toolchain.
    echo Download from: https://www.mingw-w64.org/
    exit /b 1
)

REM 检查G++
g++ --version >nul 2>&1
if errorlevel 1 (
    echo Error: g++ not found. Please install MinGW-w64 or similar GCC toolchain.
    exit /b 1
)

REM 检查Make
make --version >nul 2>&1
if errorlevel 1 (
    echo Error: make not found. Please install make utility.
    echo You can install it with MinGW-w64 or MSYS2.
    exit /b 1
)

echo Dependencies check passed.
exit /b 0

:clean_build
echo Cleaning previous build...
if exist obj rmdir /s /q obj
if exist bin rmdir /s /q bin
exit /b 0

:build_project
echo Building EFI Boot Manager...

REM 创建目录
if not exist obj mkdir obj
if not exist bin mkdir bin

REM 使用Make构建
make all
if errorlevel 1 (
    echo Build failed!
    exit /b 1
)

echo Build successful!
exit /b 0

:show_info
echo.
echo Build Information:
echo ==================

if exist bin\bootmgr.efi (
    echo Output file: bin\bootmgr.efi
    for %%A in (bin\bootmgr.efi) do echo File size: %%~zA bytes
) else (
    echo Error: Output file not found!
)

echo.
echo Installation Instructions:
echo =========================
echo 1. Copy bin\bootmgr.efi to your EFI system partition
echo 2. Typical location: C:\EFI\Boot\bootx64.efi
echo 3. Or create custom entry in UEFI firmware
echo.
echo For testing in QEMU:
echo qemu-system-x86_64 -bios OVMF.fd -drive format=raw,file=fat:rw:bin
exit /b 0

:unknown_command
echo Unknown command: %COMMAND%
echo Use '%0 help' for usage information.
exit /b 1

:end
endlocal
