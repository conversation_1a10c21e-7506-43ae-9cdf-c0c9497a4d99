# Spacious System Environment Check Script

Write-Host "===================================" -ForegroundColor Cyan
Write-Host "Spacious System Environment Check" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

$allOk = $true

# Check GCC
Write-Host "[1/5] Checking GCC compiler..." -ForegroundColor Yellow
try {
    $gccVersion = & gcc --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ GCC found" -ForegroundColor Green
        Write-Host "  Version: $($gccVersion[0])" -ForegroundColor Gray
    } else {
        throw "GCC not found"
    }
} catch {
    Write-Host "  ❌ GCC not found" -ForegroundColor Red
    $allOk = $false
}
Write-Host ""

# Check G++
Write-Host "[2/5] Checking G++ compiler..." -ForegroundColor Yellow
try {
    $gppVersion = & g++ --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ G++ found" -ForegroundColor Green
        Write-Host "  Version: $($gppVersion[0])" -ForegroundColor Gray
    } else {
        throw "G++ not found"
    }
} catch {
    Write-Host "  ❌ G++ not found" -ForegroundColor Red
    $allOk = $false
}
Write-Host ""

# Check Make
Write-Host "[3/5] Checking Make utility..." -ForegroundColor Yellow
try {
    $makeVersion = & make --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  ✅ Make found" -ForegroundColor Green
        Write-Host "  Version: $($makeVersion[0])" -ForegroundColor Gray
    } else {
        throw "Make not found"
    }
} catch {
    Write-Host "  ❌ Make not found" -ForegroundColor Red
    $allOk = $false
}
Write-Host ""

# Check GNU-EFI headers
Write-Host "[4/5] Checking GNU-EFI headers..." -ForegroundColor Yellow
$efiPaths = @(
    "C:\msys64\mingw64\include\efi\efi.h",
    "C:\MinGW\include\efi\efi.h",
    "\usr\include\efi\efi.h"
)

$efiFound = $false
foreach ($path in $efiPaths) {
    if (Test-Path $path) {
        Write-Host "  ✅ GNU-EFI found at: $path" -ForegroundColor Green
        $efiFound = $true
        break
    }
}

if (-not $efiFound) {
    Write-Host "  ❌ GNU-EFI headers not found" -ForegroundColor Red
    $allOk = $false
}
Write-Host ""

# Check project files
Write-Host "[5/5] Checking project files..." -ForegroundColor Yellow
if (Test-Path "src\efi_main.cpp") {
    Write-Host "  ✅ Source files found" -ForegroundColor Green
} else {
    Write-Host "  ❌ Source files missing" -ForegroundColor Red
    $allOk = $false
}

if (Test-Path "Makefile") {
    Write-Host "  ✅ Makefile found" -ForegroundColor Green
} else {
    Write-Host "  ❌ Makefile missing" -ForegroundColor Red
    $allOk = $false
}
Write-Host ""

# Summary
Write-Host "===================================" -ForegroundColor Cyan
Write-Host "Environment Summary" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

if ($allOk) {
    Write-Host "✅ All dependencies satisfied!" -ForegroundColor Green
    Write-Host "Ready to build Spacious System EFI Boot Manager." -ForegroundColor Green
    Write-Host ""
    Write-Host "Run: .\build_simple.bat" -ForegroundColor Yellow
} else {
    Write-Host "❌ Missing dependencies detected!" -ForegroundColor Red
    Write-Host ""
    Write-Host "📋 Installation Guide:" -ForegroundColor Yellow
    Write-Host "See WINDOWS_SETUP.md for detailed setup instructions." -ForegroundColor Gray
    Write-Host ""
    Write-Host "🚀 Quick Setup (Recommended):" -ForegroundColor Yellow
    Write-Host "1. Install MSYS2 from https://www.msys2.org/" -ForegroundColor Gray
    Write-Host "2. Run: pacman -S base-devel mingw-w64-x86_64-gcc mingw-w64-x86_64-gnu-efi" -ForegroundColor Gray
    Write-Host "3. Add C:\msys64\mingw64\bin to your PATH" -ForegroundColor Gray
    Write-Host "4. Restart command prompt and run this check again" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
