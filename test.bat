@echo off
REM EFI Boot Manager 测试脚本

setlocal enabledelayedexpansion

echo ===================================
echo EFI Boot Manager Test Script
echo ===================================

REM 检查是否已构建
if not exist bin\bootmgr.efi (
    echo Error: bootmgr.efi not found. Please build the project first.
    echo Run: build.bat
    exit /b 1
)

echo Testing EFI Boot Manager...
echo.

REM 显示文件信息
echo File Information:
echo =================
for %%A in (bin\bootmgr.efi) do (
    echo File: %%~nxA
    echo Size: %%~zA bytes
    echo Path: %%~fA
)
echo.

REM 检查文件类型
echo Checking file format...
file bin\bootmgr.efi 2>nul || echo Note: 'file' command not available

echo.
echo Testing Options:
echo ================
echo 1. Test in QEMU (requires QEMU and OVMF)
echo 2. Create test USB image
echo 3. Validate EFI structure
echo 4. Exit
echo.

set /p choice="Select test option (1-4): "

if "%choice%"=="1" goto :test_qemu
if "%choice%"=="2" goto :test_usb
if "%choice%"=="3" goto :test_validate
if "%choice%"=="4" goto :end
goto :invalid_choice

:test_qemu
echo.
echo Testing in QEMU...
echo Note: This requires QEMU and OVMF firmware to be installed.
echo.

REM 检查QEMU
qemu-system-x86_64 --version >nul 2>&1
if errorlevel 1 (
    echo Error: QEMU not found. Please install QEMU.
    echo Download from: https://www.qemu.org/download/
    goto :end
)

REM 创建临时测试目录
if not exist test_env mkdir test_env
if not exist test_env\EFI mkdir test_env\EFI
if not exist test_env\EFI\Boot mkdir test_env\EFI\Boot

REM 复制EFI文件
copy bin\bootmgr.efi test_env\EFI\Boot\bootx64.efi >nul

echo Starting QEMU test environment...
echo Press Ctrl+Alt+G to release mouse, Ctrl+Alt+Q to quit QEMU
echo.

REM 启动QEMU (需要OVMF固件)
qemu-system-x86_64 ^
    -bios OVMF.fd ^
    -drive format=raw,file=fat:rw:test_env ^
    -m 512 ^
    -net none ^
    -display gtk

goto :end

:test_usb
echo.
echo Creating test USB image...
echo This creates a virtual USB image file for testing.
echo.

if not exist test_usb mkdir test_usb
if not exist test_usb\EFI mkdir test_usb\EFI
if not exist test_usb\EFI\Boot mkdir test_usb\EFI\Boot

copy bin\bootmgr.efi test_usb\EFI\Boot\bootx64.efi >nul

echo Test USB structure created in 'test_usb' directory.
echo You can copy this structure to a real USB drive for testing.
echo.
echo USB Structure:
echo test_usb\
echo   └── EFI\
echo       └── Boot\
echo           └── bootx64.efi
echo.
goto :end

:test_validate
echo.
echo Validating EFI structure...
echo.

REM 基本文件检查
if exist bin\bootmgr.efi (
    echo ✓ EFI file exists
) else (
    echo ✗ EFI file missing
    goto :end
)

REM 检查文件大小
for %%A in (bin\bootmgr.efi) do set filesize=%%~zA
if %filesize% GTR 0 (
    echo ✓ File size: %filesize% bytes
) else (
    echo ✗ File is empty
)

REM 检查PE头 (简单检查)
REM 这里可以添加更详细的PE/EFI格式验证

echo ✓ Basic validation completed
echo.
goto :end

:invalid_choice
echo Invalid choice. Please select 1-4.
goto :end

:end
echo.
echo Test completed.
if exist test_env rmdir /s /q test_env 2>nul
endlocal
pause
