@echo off
setlocal

echo ===================================
echo Spacious System Environment Check
echo ===================================
echo.

echo Checking development environment...
echo.

REM Check GCC
echo [1/5] Checking GCC compiler...
gcc --version >nul 2>&1
if errorlevel 1 (
    echo   ❌ GCC not found
    set "GCC_OK=0"
) else (
    echo   ✅ GCC found
    gcc --version | findstr "gcc"
    set "GCC_OK=1"
)
echo.

REM Check G++
echo [2/5] Checking G++ compiler...
g++ --version >nul 2>&1
if errorlevel 1 (
    echo   ❌ G++ not found
    set "GPP_OK=0"
) else (
    echo   ✅ G++ found
    g++ --version | findstr "g++"
    set "GPP_OK=1"
)
echo.

REM Check Make
echo [3/5] Checking Make utility...
make --version >nul 2>&1
if errorlevel 1 (
    echo   ❌ Make not found
    set "MAKE_OK=0"
) else (
    echo   ✅ Make found
    make --version | findstr "Make"
    set "MAKE_OK=1"
)
echo.

REM Check GNU-EFI headers
echo [4/5] Checking GNU-EFI headers...
if exist "C:\msys64\mingw64\include\efi\efi.h" (
    echo   ✅ GNU-EFI found in MSYS2
    set "EFI_OK=1"
) else if exist "C:\MinGW\include\efi\efi.h" (
    echo   ✅ GNU-EFI found in MinGW
    set "EFI_OK=1"
) else if exist "\usr\include\efi\efi.h" (
    echo   ✅ GNU-EFI found in system
    set "EFI_OK=1"
) else (
    echo   ❌ GNU-EFI headers not found
    set "EFI_OK=0"
)
echo.

REM Check project files
echo [5/5] Checking project files...
if exist "src\efi_main.cpp" (
    echo   ✅ Source files found
    set "SRC_OK=1"
) else (
    echo   ❌ Source files missing
    set "SRC_OK=0"
)

if exist "Makefile" (
    echo   ✅ Makefile found
    set "MAKE_FILE_OK=1"
) else (
    echo   ❌ Makefile missing
    set "MAKE_FILE_OK=0"
)
echo.

REM Summary
echo ===================================
echo Environment Summary
echo ===================================

if "%GCC_OK%"=="1" if "%GPP_OK%"=="1" if "%MAKE_OK%"=="1" if "%EFI_OK%"=="1" if "%SRC_OK%"=="1" if "%MAKE_FILE_OK%"=="1" (
    echo ✅ All dependencies satisfied!
    echo Ready to build Spacious System EFI Boot Manager.
    echo.
    echo Run: .\build_simple.bat
    goto :end
)

echo ❌ Missing dependencies detected!
echo.

if "%GCC_OK%"=="0" (
    echo Missing: GCC compiler
)
if "%GPP_OK%"=="0" (
    echo Missing: G++ compiler  
)
if "%MAKE_OK%"=="0" (
    echo Missing: Make utility
)
if "%EFI_OK%"=="0" (
    echo Missing: GNU-EFI development headers
)
if "%SRC_OK%"=="0" (
    echo Missing: Project source files
)
if "%MAKE_FILE_OK%"=="0" (
    echo Missing: Project Makefile
)

echo.
echo 📋 Installation Guide:
echo See WINDOWS_SETUP.md for detailed setup instructions.
echo.
echo 🚀 Quick Setup (Recommended):
echo 1. Install MSYS2 from https://www.msys2.org/
echo 2. Run: pacman -S base-devel mingw-w64-x86_64-gcc mingw-w64-x86_64-gnu-efi
echo 3. Add C:\msys64\mingw64\bin to your PATH
echo 4. Restart command prompt and run this check again
echo.

:end
endlocal
pause
